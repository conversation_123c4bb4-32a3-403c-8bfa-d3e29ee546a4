defmodule RepobotWeb.WebhookController do
  use RepobotWeb, :controller

  require Logger

  alias <PERSON><PERSON>ot.Events
  alias <PERSON>obot.{Repositories}
  alias Repobot.Handlers.GitHub.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ullRequest<PERSON><PERSON><PERSON>, Installation<PERSON><PERSON><PERSON>}
  alias Plug.Conn
  @github_event_header "x-github-event"

  plug :cache_raw_body

  def handle(conn, params) do
    with :ok <- signature_verifier().verify_signature(conn),
         result <- handle_event(get_req_header(conn, @github_event_header), params),
         :ok <- normalize_handler_result(result) do
      json(conn, %{status: "ok"})
    else
      {:error, reason} ->
        Logger.error("Webhook error: #{inspect(reason)}")
        send_resp(conn, 400, "Bad Request")
    end
  end

  # Handle repository events
  defp handle_event(
         ["repository"],
         %{"action" => "created", "repository" => repository} = payload
       ) do
    github_id = repository["id"]
    full_name = repository["full_name"]
    owner_login = repository["owner"]["login"]

    Logger.info("Repository creation webhook received",
      github_id: github_id,
      full_name: full_name,
      owner: owner_login
    )

    # Find the organization for this repository owner
    case Repobot.Accounts.get_organization_by_name(owner_login) do
      nil ->
        Logger.warning(
          "No organization found for repository owner: #{owner_login}, skipping event creation"
        )

        :ok

      organization ->
        # Create event and schedule worker for processing
        case Events.create_and_schedule_event(
               "github.repository.created",
               payload,
               organization.id,
               nil,
               nil
             ) do
          {:ok, event} ->
            Logger.info("Repository creation event created and worker scheduled",
              event_id: event.id,
              github_id: github_id,
              full_name: full_name,
              organization_id: organization.id
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to create repository creation event",
              github_id: github_id,
              full_name: full_name,
              organization_id: organization.id,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end

  # Handle repository deletion events
  defp handle_event(
         ["repository"],
         %{"action" => "deleted", "repository" => repository} = payload
       ) do
    github_id = repository["id"]
    full_name = repository["full_name"]

    Logger.info("Repository deletion webhook received",
      github_id: github_id,
      full_name: full_name
    )

    # Find the repository to get organization_id for event creation
    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping deletion event creation",
          github_id: github_id,
          full_name: full_name
        )

        :ok

      repo ->
        # Create event and schedule worker for processing
        case Events.create_and_schedule_event(
               "github.repository.deleted",
               payload,
               repo.organization_id,
               nil,
               repo.id
             ) do
          {:ok, event} ->
            Logger.info("Repository deletion event created and worker scheduled",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              full_name: full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to create repository deletion event",
              github_id: github_id,
              repository_id: repo.id,
              full_name: full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end

  # Handle repository edit events (including renames, metadata changes, etc.)
  defp handle_event(
         ["repository"],
         %{"action" => "edited", "repository" => repository} = payload
       ) do
    github_id = repository["id"]
    full_name = repository["full_name"]

    Logger.info("Repository edit webhook received",
      github_id: github_id,
      full_name: full_name
    )

    # Find the repository to get organization_id for event creation
    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping edit event creation",
          github_id: github_id,
          full_name: full_name
        )

        :ok

      repo ->
        # Create event and schedule worker for processing
        case Events.create_and_schedule_event(
               "github.repository.edited",
               payload,
               repo.organization_id,
               nil,
               repo.id
             ) do
          {:ok, event} ->
            Logger.info("Repository edit event created and worker scheduled",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              full_name: full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to create repository edit event",
              github_id: github_id,
              repository_id: repo.id,
              full_name: full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end

  # Handle repository rename events
  defp handle_event(
         ["repository"],
         %{"action" => "renamed", "repository" => repository, "changes" => changes} = payload
       ) do
    github_id = repository["id"]
    new_full_name = repository["full_name"]
    old_name = get_in(changes, ["repository", "name", "from"])

    Logger.info("Repository rename webhook received",
      github_id: github_id,
      old_name: old_name,
      new_full_name: new_full_name
    )

    # Find the repository to get organization_id for event creation
    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping rename event creation",
          github_id: github_id,
          old_name: old_name,
          new_full_name: new_full_name
        )

        :ok

      repo ->
        # Create event and schedule worker for processing
        case Events.create_and_schedule_event(
               "github.repository.renamed",
               payload,
               repo.organization_id,
               nil,
               repo.id
             ) do
          {:ok, event} ->
            Logger.info("Repository rename event created and worker scheduled",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              old_name: old_name,
              new_full_name: new_full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to create repository rename event",
              github_id: github_id,
              repository_id: repo.id,
              old_name: old_name,
              new_full_name: new_full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end

  # Handle push events
  defp handle_event(["push"], payload) do
    PushHandler.handle(payload)
  end

  # Handle pull request events
  defp handle_event(["pull_request"], payload) do
    PullRequestHandler.handle(payload)
  end

  # Handle installation events
  defp handle_event(["installation"], payload) do
    Logger.info("Installation event received: #{payload["action"]}")
    InstallationHandler.handle(payload)
  end

  # Handle other events with repository context
  defp handle_event([event_type], %{"repository" => %{"full_name" => full_name}} = payload)
       when not is_nil(full_name) do
    # Find the repository and organization based on the repository full name
    case Repositories.get_repository_by(full_name: full_name) do
      nil ->
        Logger.info("Repository not found in database: #{full_name}")
        :ok

      repository ->
        # Log the event with github namespace
        Events.log_github_event(
          event_type,
          payload,
          repository.organization_id,
          nil,
          repository.id
        )

        :ok
    end
  end

  # Handle any other events
  defp handle_event([event_type], _payload) do
    Logger.debug("Unhandled event type: #{event_type}")
    :ok
  end

  defp handle_event(_, _) do
    :ok
  end

  defp signature_verifier do
    Application.get_env(:repobot, :signature_verifier, Repobot.GitHub.SignatureVerifier.Default)
  end

  defp cache_raw_body(conn, _opts) do
    {:ok, body, conn} = Conn.read_body(conn)
    conn = update_in(conn.assigns[:raw_body], &[body | &1 || []])
    conn
  end

  # Normalize handler results to :ok or {:error, reason}
  defp normalize_handler_result(:ok), do: :ok
  defp normalize_handler_result({:ok, _results}), do: :ok
  defp normalize_handler_result({:error, reason}), do: {:error, reason}
end

defmodule Repobot.SourceFile do
  use Ecto.<PERSON>hema

  @derive {Jason.Encoder,
           only: [
             :id,
             :user_id,
             :name,
             :target_path,
             :is_template,
             :category_id,
             :source_repository_id,
             :repository_file_id,
             :read_only,
             :inserted_at,
             :updated_at
           ]}

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "source_files" do
    field :name, :string
    field :content, :string
    field :template_vars, :map, default: %{}
    field :target_path, :string
    field :is_template, :boolean, default: false
    field :read_only, :boolean, default: false

    belongs_to :user, Repobot.Accounts.User
    belongs_to :organization, Repobot.Accounts.Organization
    belongs_to :category, Repobot.Category
    belongs_to :source_repository, Repobot.Repository
    belongs_to :repository_file, Repobot.RepositoryFile
    has_many :pull_requests, Repobot.PullRequest

    many_to_many :repositories, Repobot.Repository,
      join_through: Repobot.RepositorySourceFile,
      on_replace: :delete

    many_to_many :folders, Repobot.Folder,
      join_through: Repobot.SourceFileFolder,
      on_replace: :delete

    many_to_many :tags, Repobot.Tag,
      join_through: "source_file_tags",
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(source_file, attrs \\ %{}) do
    source_file
    |> cast(attrs, [
      :name,
      :content,
      :user_id,
      :template_vars,
      :target_path,
      :is_template,
      :organization_id,
      :category_id,
      :source_repository_id,
      :repository_file_id,
      :read_only
    ])
    |> validate_required([:name, :user_id, :organization_id])
    |> validate_not_read_only()
    |> maybe_handle_template_conversion()
    |> maybe_set_target_path()
    |> validate_required([:target_path])
    |> maybe_extract_template_vars()
  end

  defp validate_not_read_only(changeset) do
    # Only validate if this is an update operation (not a new record)
    if changeset.data.id do
      current_read_only = changeset.data.read_only

      # If the source file is currently read-only, prevent any changes except for read_only itself
      if current_read_only do
        # Check if any fields other than read_only are being changed
        changed_fields = Map.keys(changeset.changes) -- [:read_only]

        if not Enum.empty?(changed_fields) do
          add_error(
            changeset,
            :read_only,
            "cannot modify read-only source file - it is managed by GitHub events from its template repository"
          )
        else
          changeset
        end
      else
        changeset
      end
    else
      # New record, no validation needed
      changeset
    end
  end

  defp maybe_handle_template_conversion(changeset) do
    # Get the current value from the struct data
    current_is_template = changeset.data.is_template
    new_is_template = get_change(changeset, :is_template)
    name = get_field(changeset, :name)
    target_path_change = get_change(changeset, :target_path)

    # Check if we're converting from non-template to template OR updating an existing template
    case {current_is_template, new_is_template} do
      {false, true} ->
        # Converting to template - handle different scenarios
        handle_template_conversion(changeset, name, target_path_change)

      {true, true} ->
        # Already a template, but check if target_path needs correction
        # This handles the case where user manually sets target_path with .liquid extension
        if not is_nil(target_path_change) and String.ends_with?(target_path_change, ".liquid") do
          # User set target_path with .liquid extension - extract original name for target_path
          original_name = String.slice(target_path_change, 0..-8//1)
          liquid_name = add_liquid_extension(name)

          changeset
          |> put_change(:name, liquid_name)
          |> put_change(:target_path, original_name)
        else
          changeset
        end

      {true, nil} ->
        # Already a template, is_template not being changed, but check if target_path needs correction
        if not is_nil(target_path_change) and String.ends_with?(target_path_change, ".liquid") do
          # User set target_path with .liquid extension - extract original name for target_path
          original_name = String.slice(target_path_change, 0..-8//1)
          liquid_name = add_liquid_extension(name)

          changeset
          |> put_change(:name, liquid_name)
          |> put_change(:target_path, original_name)
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp handle_template_conversion(changeset, name, target_path_change) do
    case {name, target_path_change} do
      {name, nil} when not is_nil(name) ->
        # No target_path provided - add .liquid extension to name and set target_path to original name
        # Check if the original file (from data) already had .liquid extension
        original_name = changeset.data.name

        if String.ends_with?(name, ".liquid") and original_name == name do
          # File originally had .liquid extension - keep it as-is for both name and target_path
          changeset
          |> put_change(:name, name)
          |> put_change(:target_path, name)
        else
          # Either file doesn't have .liquid extension, or the .liquid was added during conversion
          # Extract the original name for target_path
          if String.ends_with?(name, ".liquid") do
            original_name = String.slice(name, 0..-8//1)
            liquid_name = add_liquid_extension(original_name)

            changeset
            |> put_change(:name, liquid_name)
            |> put_change(:target_path, original_name)
          else
            liquid_name = add_liquid_extension(name)

            changeset
            |> put_change(:name, liquid_name)
            |> put_change(:target_path, name)
          end
        end

      {name, target_path} when not is_nil(name) and not is_nil(target_path) ->
        # Both name and target_path provided - ensure proper template conversion
        if String.ends_with?(target_path, ".liquid") do
          # User set target_path with .liquid extension - extract original name for target_path
          original_name = String.slice(target_path, 0..-8//1)
          liquid_name = add_liquid_extension(name)

          changeset
          |> put_change(:name, liquid_name)
          |> put_change(:target_path, original_name)
        else
          # target_path doesn't have .liquid - use it as target_path and add .liquid to name
          liquid_name = add_liquid_extension(name)

          changeset
          |> put_change(:name, liquid_name)
          |> put_change(:target_path, target_path)
        end

      _ ->
        changeset
    end
  end

  defp maybe_set_target_path(changeset) do
    # Check if we're in any template conversion scenario
    current_is_template = changeset.data.is_template
    new_is_template = get_change(changeset, :is_template)
    is_converting_to_template = current_is_template == false && new_is_template == true
    is_converting_from_template = current_is_template == true && new_is_template == false
    is_template_conversion = is_converting_to_template || is_converting_from_template

    # Check if target_path is already set (either as a change or in the data)
    target_path_change = get_change(changeset, :target_path)
    current_target_path = changeset.data.target_path
    has_target_path = target_path_change != nil || current_target_path != nil

    name_change = get_change(changeset, :name)

    case {has_target_path, is_template_conversion} do
      {false, false} ->
        # target_path not set and not in template conversion, use name if available
        case name_change do
          nil ->
            changeset

          name ->
            put_change(changeset, :target_path, name)
        end

      _ ->
        # target_path already exists or we're in template conversion, don't override
        changeset
    end
  end

  defp add_liquid_extension(filename) do
    if String.ends_with?(filename, ".liquid") do
      filename
    else
      filename <> ".liquid"
    end
  end

  defp maybe_extract_template_vars(changeset) do
    if get_change(changeset, :content) do
      content = get_field(changeset, :content)
      vars = extract_template_vars(content)
      put_change(changeset, :template_vars, Map.new(vars, &{&1, nil}))
    else
      changeset
    end
  end

  defp extract_template_vars(content) do
    case Solid.parse(content) do
      {:ok, template} ->
        template
        |> scan_names()
        |> MapSet.new()
        |> MapSet.to_list()

      {:error, _reason} ->
        # If parsing fails, assume it's not a template and return empty list
        []
    end
  end

  defp scan_names(template) when is_list(template) do
    template
    |> Enum.flat_map(fn
      {:variable, name} -> [name]
      {_, _, children} when is_list(children) -> scan_names(children)
      _ -> []
    end)
  end

  # Handle non-list templates gracefully
  defp scan_names(_), do: []
end

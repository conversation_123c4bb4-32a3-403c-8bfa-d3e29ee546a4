defmodule Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted do
  @moduledoc """
  Oban worker for handling GitHub repository.deleted webhook events.

  This worker processes repository deletion events from GitHub webhooks,
  deletes the repository from the database with proper cleanup, and sends notifications via Oban.Notifier.
  """

  use Repobot.Workers.Worker, queue: :default

  alias Repobot.Events
  alias Repobot.Repositories

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"event_id" => event_id}} = job) do
    log_job_start(job)

    case Events.get_event(event_id) do
      nil ->
        reason = "Event not found: #{event_id}"
        log_job_error(job, reason)
        {:error, reason}

      event ->
        case process_repository_deleted_event(event) do
          :ok ->
            # Try to update event status to completed, but handle case where event was deleted
            try do
              Events.update_event_status(event, "completed")
            rescue
              Ecto.StaleEntryError ->
                # Event was deleted as part of repository cleanup, which is expected
                Logger.info("Event was deleted as part of repository cleanup",
                  event_id: event_id,
                  event_type: event.type
                )
            end

            log_job_success(job, %{event_id: event_id, event_type: event.type})
            :ok

          {:error, reason} ->
            # Update event status to failed
            Events.update_event_status(event, "failed")
            log_job_error(job, reason, %{event_id: event_id, event_type: event.type})
            {:error, reason}
        end
    end
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)
    reason = "Missing event_id in job arguments"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Process the repository deleted event
  defp process_repository_deleted_event(%Events.Event{} = event) do
    payload = event.payload
    repository_data = payload["repository"]

    github_id = repository_data["id"]
    full_name = repository_data["full_name"]

    Logger.info("Processing repository deletion event",
      event_id: event.id,
      github_id: github_id,
      full_name: full_name
    )

    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping deletion",
          event_id: event.id,
          github_id: github_id,
          full_name: full_name
        )

        :ok

      repository ->
        # Send notification about repository deletion before deleting
        send_repository_deleted_notification(event, repository)

        case Repositories.delete_repository_with_cleanup(repository) do
          {:ok, deleted_repository} ->
            Logger.info("Repository successfully deleted from database",
              event_id: event.id,
              github_id: github_id,
              full_name: full_name,
              repository_id: deleted_repository.id
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to delete repository from database",
              event_id: event.id,
              github_id: github_id,
              full_name: full_name,
              repository_id: repository.id,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end

  # Send notification about repository deletion via Oban.Notifier
  defp send_repository_deleted_notification(%Events.Event{} = event, repository) do
    notification_data = %{
      event: "repository_deleted",
      repository_id: repository.id,
      repository_name: repository.name,
      repository_full_name: repository.full_name,
      organization_id: event.organization_id,
      event_id: event.id
    }

    # Send to general repository events channel
    Oban.Notifier.notify(Oban, :repository_events, notification_data)

    # Send to organization-specific channel if we have an organization_id
    if event.organization_id do
      org_channel = String.to_atom("repository:#{event.organization_id}")
      Oban.Notifier.notify(Oban, org_channel, notification_data)
    end

    Logger.info("Repository deletion notification sent",
      repository_id: repository.id,
      full_name: repository.full_name,
      organization_id: event.organization_id
    )
  rescue
    e ->
      Logger.warning("Failed to send repository deletion notification: #{Exception.message(e)}",
        event_id: event.id,
        event_type: event.type,
        repository_id: repository.id
      )
  end
end

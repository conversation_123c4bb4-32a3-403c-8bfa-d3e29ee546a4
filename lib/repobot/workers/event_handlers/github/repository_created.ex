defmodule Repobot.Workers.EventHandlers.GitHub.RepositoryCreated do
  @moduledoc """
  Oban worker for handling GitHub repository.created webhook events.

  This worker processes repository creation events from GitHub webhooks,
  creates the repository in the database, and sends notifications via Oban.Notifier.
  """

  use Repobot.Workers.Worker, queue: :default

  alias Repobot.Events
  alias Repobot.Repositories
  alias Repobot.Accounts

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"event_id" => event_id}} = job) do
    log_job_start(job)

    case Events.get_event(event_id) do
      nil ->
        reason = "Event not found: #{event_id}"
        log_job_error(job, reason)
        {:error, reason}

      event ->
        case process_repository_created_event(event) do
          {:ok, repository} ->
            # Update event status to completed
            Events.update_event_status(event, "completed")

            # Send notification about repository creation
            send_repository_created_notification(event, repository)

            log_job_success(job, %{
              event_id: event_id,
              event_type: event.type,
              repository_id: repository.id
            })

            :ok

          {:error, reason} ->
            # Update event status to failed
            Events.update_event_status(event, "failed")
            log_job_error(job, reason, %{event_id: event_id, event_type: event.type})
            {:error, reason}
        end
    end
  end

  def perform(%Oban.Job{} = job) do
    log_job_start(job)
    reason = "Missing event_id in job arguments"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end

  # Process the repository created event
  defp process_repository_created_event(%Events.Event{} = event) do
    payload = event.payload
    repository_data = payload["repository"]

    github_id = repository_data["id"]
    full_name = repository_data["full_name"]

    Logger.info("Processing repository creation event",
      event_id: event.id,
      github_id: github_id,
      full_name: full_name
    )

    case create_repository_from_webhook(repository_data) do
      {:ok, repository} ->
        Logger.info("Repository successfully created via event worker",
          event_id: event.id,
          github_id: github_id,
          repository_id: repository.id,
          full_name: repository.full_name
        )

        {:ok, repository}

      {:error, reason} ->
        Logger.error("Failed to create repository from event worker",
          event_id: event.id,
          github_id: github_id,
          full_name: full_name,
          error: inspect(reason)
        )

        {:error, reason}
    end
  end

  # Create a repository in the database from GitHub webhook data
  defp create_repository_from_webhook(repository_data) do
    owner_login = repository_data["owner"]["login"]

    # Find the organization for this repository owner
    case Accounts.get_organization_by_name(owner_login) do
      nil ->
        Logger.warning("No organization found for repository owner: #{owner_login}")
        {:error, :organization_not_found}

      organization ->
        # Check if repository already exists
        case Repositories.get_repository_by(full_name: repository_data["full_name"]) do
          nil ->
            # Create new repository
            attrs = %{
              name: repository_data["name"],
              owner: owner_login,
              full_name: repository_data["full_name"],
              language: repository_data["language"],
              fork: repository_data["fork"] || false,
              private: repository_data["private"] || false,
              data: repository_data,
              organization_id: organization.id
            }

            Repositories.create_repository(attrs)

          existing_repo ->
            # Repository already exists, just return it
            {:ok, existing_repo}
        end
    end
  end

  # Send notification about repository creation via Oban.Notifier
  defp send_repository_created_notification(%Events.Event{} = event, repository) do
    repository_data = event.payload["repository"]

    notification_data = %{
      event: "repository_created",
      repository: %{
        id: repository.id,
        name: repository_data["name"],
        full_name: repository_data["full_name"],
        owner: repository_data["owner"]["login"],
        private: repository_data["private"],
        description: repository_data["description"],
        default_branch: repository_data["default_branch"]
      }
    }

    # Send to general repository events channel
    Oban.Notifier.notify(Oban, :repository_events, notification_data)

    # Send to organization-specific channel if we have an organization_id
    if event.organization_id do
      org_channel = String.to_atom("repository:#{event.organization_id}")
      Oban.Notifier.notify(Oban, org_channel, notification_data)
    end

    Logger.info("Repository creation notification sent",
      repository_id: repository.id,
      full_name: repository.full_name,
      organization_id: event.organization_id
    )
  rescue
    e ->
      Logger.warning("Failed to send repository creation notification: #{Exception.message(e)}",
        event_id: event.id,
        event_type: event.type,
        repository_id: repository.id
      )
  end
end

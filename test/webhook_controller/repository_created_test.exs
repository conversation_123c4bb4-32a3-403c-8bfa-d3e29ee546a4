defmodule RepobotWeb.WebhookController.RepositoryCreatedTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.{Events, Repositories}
  alias Repobot.Workers.EventHandlers.GitHub.RepositoryCreated

  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    organization = organization_fixture()
    user = user_fixture(%{default_organization_id: organization.id})

    %{
      organization: organization,
      user: user
    }
  end

  describe "repository created webhook" do
    test "successfully creates repository in database", %{
      conn: conn,
      organization: organization
    } do
      # Repository data from GitHub webhook
      repository_data = %{
        "id" => 123_456_789,
        "name" => "new-repo",
        "full_name" => "#{organization.name}/new-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A new repository",
        "default_branch" => "main",
        "owner" => %{
          "login" => organization.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")

      # Count repositories before webhook
      repo_count_before = length(Repositories.list_repositories())

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) == 1

      event = List.first(events)
      assert event.type == "github.repository.created"
      assert event.organization_id == organization.id
      assert event.payload == payload

      # Verify repository was created in database (worker executed inline)
      repos_after = Repositories.list_repositories()
      assert length(repos_after) == repo_count_before + 1

      # Find the created repository
      created_repo = Enum.find(repos_after, &(&1.full_name == "#{organization.name}/new-repo"))
      assert created_repo != nil
      assert created_repo.name == "new-repo"
      assert created_repo.owner == organization.name
      assert created_repo.language == "Elixir"
      assert created_repo.fork == false
      assert created_repo.private == true
      assert created_repo.organization_id == organization.id
      assert created_repo.data == repository_data

      # Verify event status was updated to completed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "handles repository already exists", %{
      conn: conn,
      organization: organization
    } do
      # Create an existing repository
      existing_repo =
        create_repository(%{
          name: "existing-repo",
          full_name: "#{organization.name}/existing-repo",
          organization_id: organization.id
        })

      # Repository data for the same repository
      repository_data = %{
        "id" => 123_456_789,
        "name" => "existing-repo",
        "full_name" => "#{organization.name}/existing-repo",
        "language" => "JavaScript",
        "fork" => false,
        "private" => false,
        "description" => "Updated description",
        "default_branch" => "main",
        "owner" => %{
          "login" => organization.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")

      # Count repositories before webhook
      repo_count_before = length(Repositories.list_repositories())

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) == 1

      event = List.first(events)
      assert event.type == "github.repository.created"
      assert event.organization_id == organization.id

      # Verify no new repository was created
      repos_after = Repositories.list_repositories()
      assert length(repos_after) == repo_count_before

      # Verify existing repository wasn't modified
      unchanged_repo = Repositories.get_repository!(existing_repo.id)
      assert unchanged_repo.name == existing_repo.name
      assert unchanged_repo.full_name == existing_repo.full_name

      # Verify event status was updated to completed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "handles organization not found by skipping event creation", %{conn: conn} do
      payload = %{
        "action" => "created",
        "repository" => %{
          "id" => 123_456_789,
          "name" => "new-repo",
          "full_name" => "unknown-org/new-repo",
          "owner" => %{"login" => "unknown-org"},
          "private" => false,
          "language" => "Elixir",
          "fork" => false,
          "description" => "A new repository",
          "default_branch" => "main"
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify no event was created since organization was not found
      events = Events.list_events()
      assert length(events) == 0

      # Verify no repository was created
      repos = Repositories.list_repositories()
      assert length(repos) == 0
    end

    test "worker handles missing event", %{organization: _organization} do
      # Create a job with non-existent event_id (using a valid binary_id format)
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryCreated",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => non_existent_id}
      }

      result = RepositoryCreated.perform(job)
      assert {:error, error_message} = result
      assert error_message == "Event not found: #{non_existent_id}"
    end

    test "worker handles invalid job arguments", %{organization: _organization} do
      # Create a job without event_id
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryCreated",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"invalid" => "args"}
      }

      result = RepositoryCreated.perform(job)
      assert {:error, "Missing event_id in job arguments"} = result
    end

    test "handles database creation failure", %{conn: conn, organization: organization} do
      # Repository data with invalid attributes to trigger changeset error
      repository_data = %{
        # Invalid name to trigger validation error
        "id" => 123_456_789,
        "name" => nil,
        "full_name" => "#{organization.name}/invalid-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A repository with invalid data",
        "default_branch" => "main",
        "owner" => %{
          "login" => organization.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")

      # Count repositories before webhook
      repo_count_before = length(Repositories.list_repositories())

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) == 1

      event = List.first(events)
      assert event.type == "github.repository.created"
      assert event.organization_id == organization.id

      # Verify no repository was created in database due to validation error
      repos_after = Repositories.list_repositories()
      assert length(repos_after) == repo_count_before

      # Verify event status was updated to failed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "failed"
    end
  end
end
